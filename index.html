<!DOCTYPE html>
<html lang="sq">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> e <PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hijri-date@1.0.4/hijri-date.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/sq.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        .prayer-card {
            transition: all 0.3s ease;
        }
        .prayer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .active-prayer {
            background-color: #3b82f6;
            color: white;
        }
        .countdown {
            font-family: monospace;
        }
        .modal {
            transition: opacity 0.3s ease;
        }
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header with Dates -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="text-lg font-medium text-gray-700 mb-4 md:mb-0" id="gregorian-date">
                <i data-feather="calendar" class="inline mr-2"></i>
                <span>Loading date...</span>
            </div>
            <div class="text-lg font-medium text-gray-700" id="hijri-date">
                <i data-feather="moon" class="inline mr-2"></i>
                <span>Loading Hijri date...</span>
            </div>
        </div>

        <!-- Next Prayer Card -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Namazi i Ardhshëm</h2>
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-600">Namazi:</p>
                    <p class="text-3xl font-bold text-blue-600" id="next-prayer-name">Loading...</p>
                </div>
                <div>
                    <p class="text-gray-600">Kohë e mbetur:</p>
                    <p class="text-3xl font-bold countdown" id="countdown-timer">00:00:00</p>
                </div>
            </div>
        </div>

        <!-- Notes Card -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up" data-aos-delay="100">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Shënime</h2>
            <p class="text-gray-700" id="notes-content">Nuk ka shënime për sot</p>
        </div>

        <!-- Date Picker and Prayer Times -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8" data-aos="fade-up" data-aos-delay="200">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Koha e Namazit</h2>
                <div class="w-full md:w-auto">
                    <input type="text" id="date-picker" class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="Zgjidh datën">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6" id="prayer-times-container">
                <!-- Prayer times will be loaded here -->
                <div class="text-center py-4">Loading prayer times...</div>
            </div>

            <div class="bg-blue-50 rounded-lg p-4">
                <p class="text-blue-800 font-medium">Kohëzgjatja e ditës: <span id="daylight-duration">Loading...</span></p>
            </div>
        </div>

        <!-- Religious Holidays Button -->
        <div class="text-center mb-8" data-aos="fade-up" data-aos-delay="300">
            <button id="holidays-btn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg shadow-md transition duration-300 flex items-center mx-auto">
                <i data-feather="gift" class="mr-2"></i> Festat fetare
            </button>
        </div>

        <!-- Holidays Modal -->
        <div id="holidays-modal" class="modal fixed inset-0 z-50 flex items-center justify-center opacity-0 pointer-events-none">
            <div class="modal-overlay absolute inset-0 bg-black opacity-50"></div>
            <div class="modal-container bg-white w-11/12 md:max-w-2xl mx-auto rounded shadow-lg z-50 overflow-y-auto max-h-screen">
                <div class="modal-content py-4 text-left px-6">
                    <div class="flex justify-between items-center pb-3">
                        <h3 class="text-2xl font-bold text-gray-800">Festat fetare islame për vitin 2025</h3>
                        <button id="modal-close" class="text-gray-500 hover:text-gray-700">
                            <i data-feather="x"></i>
                        </button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-3 px-4 text-left text-gray-700 font-semibold">Data</th>
                                    <th class="py-3 px-4 text-left text-gray-700 font-semibold">Festë</th>
                                </tr>
                            </thead>
                            <tbody id="holidays-table-body">
                                <!-- Holidays will be loaded here -->
                                <tr><td colspan="2" class="py-4 text-center">Loading...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize libraries
        AOS.init();
        feather.replace();

        // Albanian month names
        const albanianMonths = [
            'janar', 'shkurt', 'mars', 'prill', 'maj', 'qershor',
            'korrik', 'gusht', 'shtator', 'tetor', 'nëntor', 'dhjetor'
        ];

        // Albanian day names
        const albanianDays = [
            'E diel', 'E hënë', 'E martë', 'E mërkurë', 'E enjte', 'E premte', 'E shtunë'
        ];

        // Prayer names in Albanian
        const prayerNames = {
            'Imsaku': 'Imsak',
            'Sabahu': 'Sabah',
            'Lindja': 'Lindje',
            'Dreka': 'Drekë',
            'Ikindia': 'Ikindia',
            'Akshami': 'Aksham',
            'Jacia': 'Jacia',
            'Festat': 'Festë',
            'Shenime': 'Shënime'
        };

        // Prayer order for determining next prayer
        const prayerOrder = ['Imsaku', 'Sabahu', 'Lindja', 'Dreka', 'Ikindia', 'Akshami', 'Jacia'];

        // Global variables
        let prayerData = [];
        let selectedDate = new Date();
        let nextPrayerTimer = null;

        // DOM elements
        const gregorianDateEl = document.getElementById('gregorian-date');
        const hijriDateEl = document.getElementById('hijri-date');
        const nextPrayerNameEl = document.getElementById('next-prayer-name');
        const countdownTimerEl = document.getElementById('countdown-timer');
        const notesContentEl = document.getElementById('notes-content');
        const prayerTimesContainerEl = document.getElementById('prayer-times-container');
        const daylightDurationEl = document.getElementById('daylight-duration');
        const datePickerEl = document.getElementById('date-picker');
        const holidaysBtnEl = document.getElementById('holidays-btn');
        const holidaysModalEl = document.getElementById('holidays-modal');
        const modalCloseEl = document.getElementById('modal-close');
        const holidaysTableBodyEl = document.getElementById('holidays-table-body');

        // Format date in Albanian
        function formatAlbanianDate(date) {
            const dayName = albanianDays[date.getDay()];
            const day = date.getDate();
            const monthName = albanianMonths[date.getMonth()];
            const year = date.getFullYear();
            return `${dayName}, ${day} ${monthName} ${year}`;
        }

        // Format time from HH:MM to 12-hour format
        function formatTime(timeStr) {
            if (!timeStr) return '';
            const [hours, minutes] = timeStr.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'MD' : 'PD';
            const hour12 = hour % 12 || 12;
            return `${hour12}:${minutes} ${ampm}`;
        }

        // Calculate time difference between two times in HH:MM format
        function calculateTimeDifference(startTime, endTime) {
            if (!startTime || !endTime) return 'N/A';
            
            const [startHours, startMinutes] = startTime.split(':').map(Number);
            const [endHours, endMinutes] = endTime.split(':').map(Number);
            
            let totalMinutes = (endHours * 60 + endMinutes) - (startHours * 60 + startMinutes);
            
            if (totalMinutes < 0) {
                totalMinutes += 24 * 60; // Add a day if negative
            }
            
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            
            return `${hours} orë ${minutes} minuta`;
        }

        // Find prayer data for selected date
        function findPrayerData(date) {
            const day = date.getDate();
            const month = date.getMonth() + 1; // Months are 0-indexed
            const dateKey = `${day}-${albanianMonths[month - 1].substring(0, 3)}`;
            
            return prayerData.find(item => item.Date === dateKey);
        }

        // Update UI with prayer times for selected date
        function updatePrayerTimesUI(date) {
            const data = findPrayerData(date);
            if (!data) {
                prayerTimesContainerEl.innerHTML = '<div class="text-center py-4 text-red-500">Nuk u gjetën të dhëna për këtë datë</div>';
                daylightDurationEl.textContent = 'N/A';
                return;
            }

            // Update notes
            notesContentEl.textContent = data.Shenime || 'Nuk ka shënime për sot';

            // Update daylight duration
            daylightDurationEl.textContent = calculateTimeDifference(data.Lindja, data.Akshami);

            // Clear previous prayer times
            prayerTimesContainerEl.innerHTML = '';

            // Add prayer times cards
            prayerOrder.forEach(prayerKey => {
                const prayerTime = data[prayerKey];
                if (!prayerTime) return;

                const card = document.createElement('div');
                card.className = 'prayer-card bg-white rounded-lg shadow-md p-4 text-center border border-gray-200';
                card.innerHTML = `
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">${prayerNames[prayerKey]}</h3>
                    <p class="text-2xl font-bold text-blue-600">${formatTime(prayerTime)}</p>
                `;
                prayerTimesContainerEl.appendChild(card);
            });
        }

        // Determine next prayer and update countdown
        function updateNextPrayer() {
            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = currentHours * 60 + currentMinutes;

            const data = findPrayerData(now);
            if (!data) return;

            let nextPrayer = null;
            let nextPrayerTime = null;

            // Check each prayer in order to find the next one
            for (const prayerKey of prayerOrder) {
                const prayerTime = data[prayerKey];
                if (!prayerTime) continue;

                const [hours, minutes] = prayerTime.split(':').map(Number);
                const prayerTimeInMinutes = hours * 60 + minutes;

                if (prayerTimeInMinutes > currentTimeInMinutes) {
                    nextPrayer = prayerKey;
                    nextPrayerTime = prayerTime;
                    break;
                }
            }

            // If no next prayer found today, use first prayer of next day
            if (!nextPrayer) {
                const tomorrow = new Date(now);
                tomorrow.setDate(tomorrow.getDate() + 1);
                const tomorrowData = findPrayerData(tomorrow);
                
                if (tomorrowData && tomorrowData[prayerOrder[0]]) {
                    nextPrayer = prayerOrder[0];
                    nextPrayerTime = tomorrowData[prayerOrder[0]];
                }
            }

            // Update UI
            if (nextPrayer && nextPrayerTime) {
                nextPrayerNameEl.textContent = prayerNames[nextPrayer];
                startCountdown(nextPrayerTime);
            } else {
                nextPrayerNameEl.textContent = 'Nuk u gjet namaz i ardhshëm';
                countdownTimerEl.textContent = '--:--:--';
            }
        }

        // Start countdown to next prayer
        function startCountdown(prayerTime) {
            clearInterval(nextPrayerTimer);

            function updateCountdown() {
                const now = new Date();
                const [hours, minutes] = prayerTime.split(':').map(Number);
                
                let prayerDate = new Date(now);
                prayerDate.setHours(hours, minutes, 0, 0);
                
                // If prayer time has passed today, set for tomorrow
                if (prayerDate < now) {
                    prayerDate.setDate(prayerDate.getDate() + 1);
                }
                
                const diff = prayerDate - now;
                
                if (diff <= 0) {
                    // Prayer time reached, update next prayer
                    updateNextPrayer();
                    return;
                }
                
                // Calculate hours, minutes, seconds
                const totalSeconds = Math.floor(diff / 1000);
                const hoursLeft = Math.floor(totalSeconds / 3600);
                const minutesLeft = Math.floor((totalSeconds % 3600) / 60);
                const secondsLeft = totalSeconds % 60;
                
                // Update countdown display
                countdownTimerEl.textContent = 
                    `${hoursLeft.toString().padStart(2, '0')}:${minutesLeft.toString().padStart(2, '0')}:${secondsLeft.toString().padStart(2, '0')}`;
            }
            
            // Update immediately and then every second
            updateCountdown();
            nextPrayerTimer = setInterval(updateCountdown, 1000);
        }

        // Load CSV data
        function loadCSVData(csvText) {
            Papa.parse(csvText, {
                header: true,
                complete: function(results) {
                    prayerData = results.data;
                    
                    // Update dates and prayer times
                    updateDates();
                    updatePrayerTimesUI(selectedDate);
                    updateNextPrayer();
                    
                    // Load holidays
                    loadHolidays();
                },
                error: function(error) {
                    console.error('Error parsing CSV:', error);
                    alert('Gabim gjatë leximit të të dhënave. Ju lutem rifreskoni faqen.');
                }
            });
        }

        // Update date displays
        function updateDates() {
            // Gregorian date
            gregorianDateEl.innerHTML = `<i data-feather="calendar" class="inline mr-2"></i>${formatAlbanianDate(selectedDate)}`;
            
            // Hijri date
            const hijriDate = new Intl.DateTimeFormat('en-u-ca-islamic', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            }).format(selectedDate);
            
            hijriDateEl.innerHTML = `<i data-feather="moon" class="inline mr-2"></i>${hijriDate}`;
            
            // Refresh icons
            feather.replace();
        }

        // Load religious holidays
        function loadHolidays() {
            holidaysTableBodyEl.innerHTML = '';
            
            const holidays = prayerData.filter(item => item.Festat && item.Festat.trim() !== '');
            
            if (holidays.length === 0) {
                holidaysTableBodyEl.innerHTML = '<tr><td colspan="2" class="py-4 text-center">Nuk ka festa fetare të regjistruara</td></tr>';
                return;
            }
            
            holidays.forEach(item => {
                const [day, monthAbbr] = item.Date.split('-');
                const monthIndex = albanianMonths.findIndex(m => m.startsWith(monthAbbr));
                const date = new Date();
                date.setMonth(monthIndex);
                date.setDate(parseInt(day));
                
                const row = document.createElement('tr');
                row.className = 'border-b border-gray-200 hover:bg-gray-50';
                row.innerHTML = `
                    <td class="py-3 px-4">${formatAlbanianDate(date)}</td>
                    <td class="py-3 px-4">${item.Festat}</td>
                `;
                holidaysTableBodyEl.appendChild(row);
            });
        }

        // Initialize date picker
        function initDatePicker() {
            flatpickr(datePickerEl, {
                locale: 'sq',
                dateFormat: 'd-m-Y',
                defaultDate: selectedDate,
                onChange: function(selectedDates) {
                    selectedDate = selectedDates[0];
                    updateDates();
                    updatePrayerTimesUI(selectedDate);
                    
                    // If selected date is today, update next prayer
                    const today = new Date();
                    if (selectedDate.toDateString() === today.toDateString()) {
                        updateNextPrayer();
                    }
                }
            });
        }

        // Event listeners
        holidaysBtnEl.addEventListener('click', function() {
            holidaysModalEl.classList.remove('opacity-0', 'pointer-events-none');
            document.body.classList.add('overflow-hidden');
        });

        modalCloseEl.addEventListener('click', function() {
            holidaysModalEl.classList.add('opacity-0', 'pointer-events-none');
            document.body.classList.remove('overflow-hidden');
        });

        // Load CSV data (in a real app, this would be from a file or API)
        // For demo purposes, we'll simulate loading after a short delay
        document.addEventListener('DOMContentLoaded', function() {
            updateDates();
            initDatePicker();
            
            // Simulate loading CSV data (in a real app, you would fetch this)
            setTimeout(() => {
                // This is where you would load your actual CSV file
                // For now, we'll use a placeholder
                console.log('Loading CSV data...');
                
                // In a real implementation, you might use:
                // fetch('prayer_times.csv')
                //     .then(response => response.text())
                //     .then(text => loadCSVData(text));
                
                // For demo, we'll just show that the UI is ready
                updatePrayerTimesUI(selectedDate);
                updateNextPrayer();
            }, 1000);
        });
    </script>
</body>
</html>
